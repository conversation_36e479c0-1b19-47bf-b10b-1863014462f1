#include "player.h"
#include <algorithm>

Player::Player(int id) 
    : id_(id), position_(0, 0), velocity_(0, 0), health_(100), speed_(200.0f) {}

Player::~Player() = default;

void Player::update(float deltaTime) {
    position_.x += velocity_.x * deltaTime;
    position_.y += velocity_.y * deltaTime;
    
    // Apply friction
    velocity_.x *= 0.9f;
    velocity_.y *= 0.9f;
    
    // Clamp position to world bounds
    position_.x = std::clamp(position_.x, -1000.0f, 1000.0f);
    position_.y = std::clamp(position_.y, -1000.0f, 1000.0f);
}

void Player::handleInput(const std::vector<uint8_t>& input) {
    if (input.empty()) return;
    
    // Simple input protocol: [direction_flags]
    uint8_t flags = input[0];
    
    velocity_.x = 0;
    velocity_.y = 0;
    
    if (flags & 0x01) velocity_.y -= speed_; // Up
    if (flags & 0x02) velocity_.y += speed_; // Down
    if (flags & 0x04) velocity_.x -= speed_; // Left
    if (flags & 0x08) velocity_.x += speed_; // Right
}

void Player::takeDamage(int damage) {
    health_ = std::max(0, health_ - damage);
}